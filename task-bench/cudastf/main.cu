/* Copyright 2024 NVIDIA Corporation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <cuda/experimental/stf.cuh>
#include <algorithm>
#include <vector>
#include <memory>
#include <atomic>
#include <cassert>

#include "core.h"
#include "timer.h"

using namespace cuda::experimental::stf;

// Matrix structure similar to StarPU's matrix_t - only manages memory buffers
struct GraphMatrix {
  size_t MT;  // Number of fields (for WAR dependency avoidance)
  size_t NT;  // Maximum width
  size_t bytes_per_task;
  
  // Only manage actual memory buffers, not logical_data
  std::vector<std::vector<std::unique_ptr<char[]>>> data;  // [field][point]
  
  GraphMatrix() : MT(0), NT(0), bytes_per_task(0) {}
  
  void initialize(size_t mt, size_t nt, size_t bytes) {
    MT = mt;
    NT = nt;
    bytes_per_task = bytes;
    data.resize(MT);
    for (size_t field = 0; field < MT; field++) {
      data[field].resize(NT);
      for (size_t point = 0; point < NT; point++) {
        data[field][point] = std::make_unique<char[]>(bytes);
      }
    }
  }
  
  char* get_buffer(long timestep, long point) {
    size_t field = timestep % MT;
    assert(point < NT);
    return data[field][point].get();
  }
};

// CUDA STF App implementation following StarPU patterns
struct CudaSTFApp : public App {
  CudaSTFApp(int argc, char **argv);
  ~CudaSTFApp();
  
  void execute_main_loop();
  void execute_timestep(size_t idx, long t);

private:
  void execute_task_no_deps(size_t graph_idx, long timestep, long point);
  void execute_task_with_deps(size_t graph_idx, long timestep, long point,
                             const std::vector<std::pair<long, long>>& deps);
  
  // Helper functions
  char* get_scratch_memory();
  char* get_host_buffer();

private:
  // STF context
  context ctx;
  
  // Similar to StarPU's mat_array - data structures for each graph
  std::vector<GraphMatrix> graph_matrices;
  
  // Similar to StarPU's extra_local_memory - pre-allocated scratch memory
  std::vector<std::unique_ptr<char[]>> scratch_memory_pool;
  size_t max_scratch_bytes;
  
  // Host buffer pool to avoid frequent allocations
  std::vector<std::unique_ptr<char[]>> host_buffer_pool;
  size_t max_output_bytes;
  std::atomic<size_t> next_buffer_id{0};
  std::atomic<size_t> next_scratch_id{0};
};

// Constructor - following StarPU initialization patterns
CudaSTFApp::CudaSTFApp(int argc, char **argv)
  : App(argc, argv)
{
  printf("Initializing CUDA STF App with %zu graphs\n", graphs.size());
  
  // 1. Calculate maximum scratch and output buffer sizes
  max_scratch_bytes = 0;
  max_output_bytes = 0;
  for (const auto& graph : graphs) {
    max_scratch_bytes = std::max(max_scratch_bytes, graph.scratch_bytes_per_task);
    max_output_bytes = std::max(max_output_bytes, graph.output_bytes_per_task);
  }
  
  // 2. Pre-allocate scratch memory pool (similar to StarPU's extra_local_memory)
  size_t scratch_pool_size = 64;  // Support 64 concurrent tasks
  if (max_scratch_bytes > 0) {
    scratch_memory_pool.resize(scratch_pool_size);
    for (size_t i = 0; i < scratch_pool_size; i++) {
      scratch_memory_pool[i] = std::make_unique<char[]>(max_scratch_bytes);
      TaskGraph::prepare_scratch(scratch_memory_pool[i].get(), max_scratch_bytes);
    }
    printf("Allocated %zu scratch buffers of %zu bytes each\n", 
           scratch_pool_size, max_scratch_bytes);
  }
  
  // 3. Pre-allocate host buffer pool
  size_t host_pool_size = 128;
  if (max_output_bytes > 0) {
    host_buffer_pool.resize(host_pool_size);
    for (size_t i = 0; i < host_pool_size; i++) {
      host_buffer_pool[i] = std::make_unique<char[]>(max_output_bytes);
    }
    printf("Allocated %zu host buffers of %zu bytes each\n", 
           host_pool_size, max_output_bytes);
  }
  
  // 4. Create data structures for each graph (similar to StarPU's mat_array initialization)
  graph_matrices.resize(graphs.size());
  for (size_t i = 0; i < graphs.size(); i++) {
    const TaskGraph& graph = graphs[i];
    GraphMatrix& mat = graph_matrices[i];
    
    // Set matrix dimensions
    size_t MT = graph.nb_fields;    // Number of fields for WAR dependency avoidance
    size_t NT = graph.max_width;    // Maximum width
    
    printf("Graph %zu: MT=%zu (fields), NT=%zu (max_width), output_bytes=%zu\n", 
           i, MT, NT, graph.output_bytes_per_task);
    
    // Initialize data structure - only allocate memory buffers
    mat.initialize(MT, NT, graph.output_bytes_per_task);
  }
  
  printf("CUDA STF initialization complete\n");
}

// Get pre-allocated scratch memory
char* CudaSTFApp::get_scratch_memory() {
  if (max_scratch_bytes == 0 || scratch_memory_pool.empty()) {
    return nullptr;
  }
  
  // Round-robin through scratch memory
  size_t id = next_scratch_id.fetch_add(1) % scratch_memory_pool.size();
  return scratch_memory_pool[id].get();
}

// Get pre-allocated host buffer
char* CudaSTFApp::get_host_buffer() {
  if (max_output_bytes == 0 || host_buffer_pool.empty()) {
    return nullptr;
  }
  
  // Round-robin through host buffers
  size_t id = next_buffer_id.fetch_add(1) % host_buffer_pool.size();
  return host_buffer_pool[id].get();
}

// Destructor
CudaSTFApp::~CudaSTFApp() {
  // Wait for all tasks to complete
  printf("Finalizing CUDA STF context\n");
  ctx.finalize();
  
  // Clean up memory pools
  scratch_memory_pool.clear();
  host_buffer_pool.clear();
  
  printf("CUDA STF cleanup complete\n");
}

// Main execution loop - following StarPU execution patterns
void CudaSTFApp::execute_main_loop() {
  display();
  
  printf("Starting main execution loop\n");
  
  // Start timing
  Timer::time_start();
  
  // Execute all graphs and all timesteps (similar to StarPU execution pattern)
  for (size_t i = 0; i < graphs.size(); i++) {
    const TaskGraph &graph = graphs[i];
    for (long t = 0; t < graph.timesteps; t++) {
      execute_timestep(i, t);
    }
  }
  
  // Wait for all tasks to complete (similar to StarPU's starpu_task_wait_for_all)
  printf("Waiting for all tasks to complete\n");
  ctx.finalize();
  
  double elapsed = Timer::time_end();
  printf("Execution completed in %.6f seconds\n", elapsed);
  report_timing(elapsed);
}

// Execute a single timestep - following StarPU's execute_timestep implementation
void CudaSTFApp::execute_timestep(size_t idx, long t) {
  const TaskGraph& g = graphs[idx];
  long offset = g.offset_at_timestep(t);
  long width = g.width_at_timestep(t);
  long dset = g.dependence_set_at_timestep(t);
  
  printf("Executing timestep %ld for graph %zu (offset=%ld, width=%ld)\n", 
         t, idx, offset, width);

  // Iterate through all points in current timestep (similar to StarPU loop)
  for (long x = offset; x < offset + width; x++) {
    // Get dependencies
    std::vector<std::pair<long, long>> deps = g.dependencies(dset, x);

    // For stencil patterns, dependencies are from previous timestep
    // The deps returned by dependencies() are point ranges, not (timestep, point) pairs
    std::vector<std::pair<long, long>> valid_deps;
    if (t > 0) {  // Only timesteps > 0 have dependencies
      valid_deps = deps;  // Use all dependencies for timesteps > 0
    }

    printf("Task (%ld, %ld): original deps=%zu, valid deps=%zu\n",
           t, x, deps.size(), valid_deps.size());

    // Execute task based on valid dependencies
    if (valid_deps.empty()) {
      // No valid dependencies
      execute_task_no_deps(idx, t, x);
    } else {
      // With valid dependencies
      execute_task_with_deps(idx, t, x, valid_deps);
    }
  }
}

// Execute task with no dependencies - following StarPU's task1 implementation
void CudaSTFApp::execute_task_no_deps(size_t graph_idx, long timestep, long point) {
  const TaskGraph& graph = graphs[graph_idx];
  GraphMatrix& mat = graph_matrices[graph_idx];

  // Get pre-allocated memory buffer
  char* buffer = mat.get_buffer(timestep, point);

  // Create logical_data only when needed for task creation
  auto output = ctx.logical_data(buffer, graph.output_bytes_per_task);

  printf("Executing task_no_deps for graph %zu, timestep %ld, point %ld\n",
         graph_idx, timestep, point);

  // Use STF task execution - call execute_point in lambda
  ctx.task(output.write())->*[=](cudaStream_t s, auto out) {
    // Get pre-allocated host buffer and scratch memory
    char* host_output = get_host_buffer();
    char* scratch_ptr = get_scratch_memory();

    // Call core execute_point method (this sets has_executed_graph flag)
    printf("Calling execute_point for graph %zu, timestep %ld, point %ld\n",
           graph_idx, timestep, point);

    // Always call execute_point to set has_executed_graph flag
    graph.execute_point(timestep, point,
                       host_output, graph.output_bytes_per_task,
                       nullptr, nullptr, 0,  // no inputs
                       scratch_ptr, graph.scratch_bytes_per_task);

#if !defined(USE_CORE_VERIFICATION)
    // Debug mode - additional output
    printf("CUDA STF Task [%ld, %ld], graph %zu\n", timestep, point, graph_idx);
#endif

    // Copy result to device
    cudaMemcpyAsync(out.data_handle(), host_output, graph.output_bytes_per_task,
                   cudaMemcpyHostToDevice, s);
  };
}

// Execute task with dependencies - following StarPU's task2/task3 implementation
void CudaSTFApp::execute_task_with_deps(size_t graph_idx, long timestep, long point,
                                       const std::vector<std::pair<long, long>>& deps) {
  const TaskGraph& graph = graphs[graph_idx];
  GraphMatrix& mat = graph_matrices[graph_idx];

  // Get pre-allocated output buffer
  char* output_buffer = mat.get_buffer(timestep, point);
  auto output = ctx.logical_data(output_buffer, graph.output_bytes_per_task);

  printf("Executing task_with_deps for graph %zu, timestep %ld, point %ld, deps=%zu\n",
         graph_idx, timestep, point, deps.size());

  // Collect input logical data - expand ranges to individual points
  std::vector<logical_data<slice<char>>> inputs;
  std::vector<long> input_points;  // Track which points we're reading

  for (const auto& dep_range : deps) {
    for (long dep_point = dep_range.first; dep_point <= dep_range.second; dep_point++) {
      char* input_buffer = mat.get_buffer(timestep - 1, dep_point);  // Previous timestep
      inputs.emplace_back(ctx.logical_data(input_buffer, graph.output_bytes_per_task));
      input_points.push_back(dep_point);
    }
  }

  printf("Expanded dependencies: ");
  for (long pt : input_points) {
    printf("(%ld,%ld) ", timestep - 1, pt);
  }
  printf("\n");

  // Handle different numbers of dependencies (similar to StarPU's approach)
  if (inputs.size() == 1) {
    // Capture the dependency info
    auto dep_point = input_points[0];
    ctx.task(inputs[0].read(), output.write())->*[=](cudaStream_t s, auto in, auto out) {
      // Get pre-allocated buffers
      char* host_output = get_host_buffer();
      char* host_input = get_host_buffer();  // Use another buffer for input
      char* scratch_ptr = get_scratch_memory();

      // Copy input from device to host
      cudaMemcpyAsync(host_input, in.data_handle(), graph.output_bytes_per_task,
                     cudaMemcpyDeviceToHost, s);
      cudaStreamSynchronize(s);

      // Prepare input pointers for execute_point
      const char* input_ptrs[] = { host_input };
      size_t input_sizes[] = { graph.output_bytes_per_task };

      printf("Calling execute_point (1 input) for graph %zu, timestep %ld, point %ld, dep=(%ld,%ld)\n",
             graph_idx, timestep, point, timestep-1, dep_point);

      // Always call execute_point to set has_executed_graph flag
      graph.execute_point(timestep, point,
                         host_output, graph.output_bytes_per_task,
                         input_ptrs, input_sizes, 1,
                         scratch_ptr, graph.scratch_bytes_per_task);

#if !defined(USE_CORE_VERIFICATION)
      // Debug mode - additional output
      printf("CUDA STF Task with 1 dep [%ld, %ld], graph %zu\n", timestep, point, graph_idx);
#endif

      // Copy result to device
      cudaMemcpyAsync(out.data_handle(), host_output, graph.output_bytes_per_task,
                     cudaMemcpyHostToDevice, s);
    };
  } else if (inputs.size() == 2) {
    // Capture the dependency info
    auto dep_point0 = input_points[0];
    auto dep_point1 = input_points[1];
    ctx.task(inputs[0].read(), inputs[1].read(), output.write())->*[=](cudaStream_t s, auto in1, auto in2, auto out) {
      // Get pre-allocated buffers
      char* host_output = get_host_buffer();
      char* host_input1 = get_host_buffer();
      char* host_input2 = get_host_buffer();
      char* scratch_ptr = get_scratch_memory();

      // Copy inputs from device to host
      cudaMemcpyAsync(host_input1, in1.data_handle(), graph.output_bytes_per_task,
                     cudaMemcpyDeviceToHost, s);
      cudaMemcpyAsync(host_input2, in2.data_handle(), graph.output_bytes_per_task,
                     cudaMemcpyDeviceToHost, s);
      cudaStreamSynchronize(s);

      // Prepare input pointers for execute_point
      const char* input_ptrs[] = { host_input1, host_input2 };
      size_t input_sizes[] = { graph.output_bytes_per_task, graph.output_bytes_per_task };

      printf("Calling execute_point (2 inputs) for graph %zu, timestep %ld, point %ld, deps=(%ld,%ld),(%ld,%ld)\n",
             graph_idx, timestep, point, timestep-1, dep_point0, timestep-1, dep_point1);

      // Always call execute_point to set has_executed_graph flag
      graph.execute_point(timestep, point,
                         host_output, graph.output_bytes_per_task,
                         input_ptrs, input_sizes, 2,
                         scratch_ptr, graph.scratch_bytes_per_task);

#if !defined(USE_CORE_VERIFICATION)
      // Debug mode - additional output
      printf("CUDA STF Task with 2 deps [%ld, %ld], graph %zu\n", timestep, point, graph_idx);
#endif

      // Copy result to device
      cudaMemcpyAsync(out.data_handle(), host_output, graph.output_bytes_per_task,
                     cudaMemcpyHostToDevice, s);
    };
  } else if (inputs.size() == 3) {
    // Handle 3 inputs
    auto dep_point0 = input_points[0];
    auto dep_point1 = input_points[1];
    auto dep_point2 = input_points[2];
    ctx.task(inputs[0].read(), inputs[1].read(), inputs[2].read(), output.write())->*[=](cudaStream_t s, auto in1, auto in2, auto in3, auto out) {
      // Get pre-allocated buffers
      char* host_output = get_host_buffer();
      char* host_input1 = get_host_buffer();
      char* host_input2 = get_host_buffer();
      char* host_input3 = get_host_buffer();
      char* scratch_ptr = get_scratch_memory();

      // Copy inputs from device to host
      cudaMemcpyAsync(host_input1, in1.data_handle(), graph.output_bytes_per_task,
                     cudaMemcpyDeviceToHost, s);
      cudaMemcpyAsync(host_input2, in2.data_handle(), graph.output_bytes_per_task,
                     cudaMemcpyDeviceToHost, s);
      cudaMemcpyAsync(host_input3, in3.data_handle(), graph.output_bytes_per_task,
                     cudaMemcpyDeviceToHost, s);
      cudaStreamSynchronize(s);

      // Prepare input pointers for execute_point
      const char* input_ptrs[] = { host_input1, host_input2, host_input3 };
      size_t input_sizes[] = { graph.output_bytes_per_task, graph.output_bytes_per_task, graph.output_bytes_per_task };

      printf("Calling execute_point (3 inputs) for graph %zu, timestep %ld, point %ld, deps=(%ld,%ld),(%ld,%ld),(%ld,%ld)\n",
             graph_idx, timestep, point, timestep-1, dep_point0, timestep-1, dep_point1, timestep-1, dep_point2);

      // Always call execute_point to set has_executed_graph flag
      graph.execute_point(timestep, point,
                         host_output, graph.output_bytes_per_task,
                         input_ptrs, input_sizes, 3,
                         scratch_ptr, graph.scratch_bytes_per_task);

#if !defined(USE_CORE_VERIFICATION)
      // Debug mode - additional output
      printf("CUDA STF Task with 3 deps [%ld, %ld], graph %zu\n", timestep, point, graph_idx);
#endif

      // Copy result to device
      cudaMemcpyAsync(out.data_handle(), host_output, graph.output_bytes_per_task,
                     cudaMemcpyHostToDevice, s);
    };
  } else {
    // For more than 3 inputs, we need a more complex approach
    printf("ERROR: CUDA STF implementation currently supports at most 3 input dependencies, got %zu\n",
           inputs.size());
  }
}

// Main function
int main(int argc, char **argv) {
  CudaSTFApp app(argc, argv);
  app.execute_main_loop();
  return 0;
}
